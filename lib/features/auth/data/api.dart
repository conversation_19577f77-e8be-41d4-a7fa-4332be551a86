import 'package:dio/dio.dart';
import 'package:e_library/features/api/dio.dart';
import 'package:e_library/features/auth/data/models/auth_request.dart';
import 'package:e_library/features/auth/data/models/auth_response.dart';

class AuthApi {
  static Future<LoginResponse> login(LoginRequest request) async {
    try {
      final response = await dio.post('/auth/login', data: request.toJson());
      return LoginResponse.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw AuthException('Invalid credentials');
      } else if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        final errorMessage = _formatValidationErrors(errors);
        throw AuthException(errorMessage);
      }
      throw AuthException('Login failed: ${e.message}');
    }
  }

  static Future<RegisterResponse> register(RegisterRequest request) async {
    try {
      final response = await dio.post('/auth/register', data: request.toJson());
      return RegisterResponse.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        final errorMessage = _formatValidationErrors(errors);
        throw AuthException(errorMessage);
      }
      throw AuthException('Registration failed: ${e.message}');
    }
  }

  static Future<LogoutResponse> logout() async {
    try {
      final response = await dio.post('/auth/logout');
      return LogoutResponse.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw AuthException('Logout failed: ${e.message}');
    }
  }

  static String _formatValidationErrors(Map<String, dynamic>? errors) {
    if (errors == null) return 'Validation failed';
    
    final errorMessages = <String>[];
    errors.forEach((field, messages) {
      if (messages is List) {
        errorMessages.addAll(messages.cast<String>());
      }
    });
    
    return errorMessages.isNotEmpty 
        ? errorMessages.join('\n') 
        : 'Validation failed';
  }
}

class AuthException implements Exception {
  final String message;
  
  const AuthException(this.message);
  
  @override
  String toString() => message;
}
