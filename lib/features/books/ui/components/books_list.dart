import 'package:dio/dio.dart';
import 'package:e_library/features/api/dio.dart';
import 'package:e_library/features/api/responses/paginated_response.dart';
import 'package:e_library/features/books/data/models/book.dart';
import 'package:e_library/features/books/data/queries.dart';
import 'package:fl_query/fl_query.dart';
import 'package:flutter/material.dart';

class BooksList extends StatelessWidget {
  const BooksList({super.key});

  @override
  Widget build(BuildContext context) {
    return QueryBuilder.withJob<PaginatedResponse<List<Book>>, dynamic, Dio>(
      job: paginatedBooksJob,
      args: dio,
      onData: (value) {
        debugPrint('onData: $value');
      },
      onError: (error) {
        debugPrint('onError: $error');
      },
      builder: (context, query) {
        if (query.isLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (query.hasError) {
          return Center(child: Text(query.error.toString()));
        }

        var items = query.data?.data ?? [];

        return ListView.builder(
          itemCount: items.length,
          itemBuilder: (BuildContext context, int index) {
            return BookListItem(book: items[index]);
          },
        );
      },
    );
  }
}

class BookListItem extends StatelessWidget {
  const BookListItem({super.key, required this.book});
  final Book book;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(book.title),
              Row(
                children: [
                  Text(
                    'Author Name',
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: Theme.of(context).hintColor,
                    ),
                  ),
                  Text(' • '),
                  Text(
                    'Publisher Name',
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: Theme.of(context).hintColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
          Text(book.formattedPrice),
        ],
      ),
    );
  }
}
